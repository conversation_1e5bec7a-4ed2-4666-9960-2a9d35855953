package service

import (
	"alltick_conf_manager/models"
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-playground/validator/v10"
	clientv3 "go.etcd.io/etcd/client/v3"
)

const (
	configPrefix    = "/alltick/"
	apikeysPrefix   = configPrefix + "apikeys/"
	productsPrefix  = configPrefix + "products/"
	receiversPrefix = configPrefix + "receivers/"
	// ... other prefixes
)

// ConfigService handles configuration logic
type ConfigService struct {
	etcdClient *clientv3.Client
	validate   *validator.Validate
}

// NewConfigService creates a new ConfigService
func NewConfigService(client *clientv3.Client) *ConfigService {
	return &ConfigService{
		etcdClient: client,
		validate:   validator.New(),
	}
}

// GetApikeys retrieves all apikeys from etcd
func (s *ConfigService) GetApikeys() ([]*models.ApiKey, error) {
	resp, err := s.etcdClient.Get(context.Background(), apikeysPrefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("failed to get apikeys from etcd: %w", err)
	}
	var apikeys []*models.ApiKey
	for _, kv := range resp.Kvs {
		var apikey models.ApiKey
		if err := json.Unmarshal(kv.Value, &apikey); err != nil {
			return nil, fmt.Errorf("failed to unmarshal apikey: %w", err)
		}
		apikeys = append(apikeys, &apikey)
	}
	return apikeys, nil
}

// CreateApiKey creates a new apikey in etcd
func (s *ConfigService) CreateApiKey(apikey *models.ApiKey) error {
	// Validate required fields
	if err := s.validate.Struct(apikey); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	key := apikeysPrefix + apikey.Name
	value, err := json.Marshal(apikey)
	if err != nil {
		return fmt.Errorf("failed to marshal apikey: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put apikey to etcd: %w", err)
	}
	return nil
}

// GetProducts retrieves all products from etcd
func (s *ConfigService) GetProducts() ([]*models.Product, error) {
	resp, err := s.etcdClient.Get(context.Background(), productsPrefix, clientv3.WithPrefix())
	if err != nil {
		return nil, fmt.Errorf("failed to get products from etcd: %w", err)
	}
	var products []*models.Product
	for _, kv := range resp.Kvs {
		var product models.Product
		if err := json.Unmarshal(kv.Value, &product); err != nil {
			return nil, fmt.Errorf("failed to unmarshal product: %w", err)
		}
		products = append(products, &product)
	}
	return products, nil
}

// CreateProduct creates a new product in etcd
func (s *ConfigService) CreateProduct(product *models.Product) error {
	// Validate required fields
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	key := productsPrefix + product.Name
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	return nil
}

// UpdateProduct updates an existing product in etcd
func (s *ConfigService) UpdateProduct(product *models.Product) error {
	// Validate required fields
	if err := s.validate.Struct(product); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	key := productsPrefix + product.Name
	value, err := json.Marshal(product)
	if err != nil {
		return fmt.Errorf("failed to marshal product: %w", err)
	}
	_, err = s.etcdClient.Put(context.Background(), key, string(value))
	if err != nil {
		return fmt.Errorf("failed to put product to etcd: %w", err)
	}
	return nil
}

// DeleteProduct deletes a product from etcd
func (s *ConfigService) DeleteProduct(name string) error {
	if name == "" {
		return fmt.Errorf("product name cannot be empty")
	}

	key := productsPrefix + name
	_, err := s.etcdClient.Delete(context.Background(), key)
	if err != nil {
		return fmt.Errorf("failed to delete product from etcd: %w", err)
	}
	return nil
}

// GetProduct retrieves a specific product by name from etcd
func (s *ConfigService) GetProduct(name string) (*models.Product, error) {
	if name == "" {
		return nil, fmt.Errorf("product name cannot be empty")
	}

	key := productsPrefix + name
	resp, err := s.etcdClient.Get(context.Background(), key)
	if err != nil {
		return nil, fmt.Errorf("failed to get product from etcd: %w", err)
	}

	if len(resp.Kvs) == 0 {
		return nil, fmt.Errorf("product not found: %s", name)
	}

	var product models.Product
	if err := json.Unmarshal(resp.Kvs[0].Value, &product); err != nil {
		return nil, fmt.Errorf("failed to unmarshal product: %w", err)
	}
	return &product, nil
}

// ... Implement dynamic allocation logic here
// WatchReceiverConfig watches for configuration changes for a specific receiver
func (s *ConfigService) WatchReceiverConfig(receiverID string, callback func([]string)) error {
	watchKey := fmt.Sprintf("/assignments/product_receiver/%s", receiverID)
	rch := s.etcdClient.Watch(context.Background(), watchKey, clientv3.WithPrefix())
	go func() {
		for wresp := range rch {
			for _, ev := range wresp.Events {
				// 根据事件类型和新的值更新 receiver 的产品列表
				// 这里需要更复杂的逻辑来处理不同的事件类型和键
				fmt.Printf("Receiver %s config changed: EventType: %s, Key: %s, Value: %s\n", receiverID, ev.Type, ev.Kv.Key, ev.Kv.Value)
				// 触发 callback，通知 receiver 更新本地配置
				// callback(最新的产品列表)
			}
		}
	}()
	return nil
}
