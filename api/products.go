package api

import (
	"alltick_conf_manager/models"
	"alltick_conf_manager/service"
	"encoding/json"
	"net/http"
	"strings"

	"github.com/go-playground/validator/v10"
)

func HandleProducts(cfgService *service.ConfigService) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Set content type for JSON responses
		w.Header().Set("Content-Type", "application/json")

		switch r.Method {
		case "GET":
			handleGetProducts(w, r, cfgService)
		case "POST":
			handleCreateProduct(w, r, cfgService)
		case "PUT":
			handleUpdateProduct(w, r, cfgService)
		case "DELETE":
			handleDeleteProduct(w, r, cfgService)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

func handleGetProducts(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	// Check if requesting a specific product by name
	productName := strings.TrimPrefix(r.URL.Path, "/products/")
	if productName != "" && productName != "products" {
		// Get specific product
		product, err := cfgService.GetProduct(productName)
		if err != nil {
			if strings.Contains(err.Error(), "not found") {
				http.Error(w, err.Error(), http.StatusNotFound)
				return
			}
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		json.NewEncoder(w).Encode(product)
		return
	}

	// Get all products
	products, err := cfgService.GetProducts()
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	json.NewEncoder(w).Encode(products)
}

func handleCreateProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	var product models.Product
	if err := json.NewDecoder(r.Body).Decode(&product); err != nil {
		http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
		return
	}

	if err := cfgService.CreateProduct(&product); err != nil {
		// Check if it's a validation error
		if _, ok := err.(validator.ValidationErrors); ok {
			http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
			return
		}
		if strings.Contains(err.Error(), "validation failed") {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]string{"message": "Product created successfully"})
}

func handleUpdateProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	var product models.Product
	if err := json.NewDecoder(r.Body).Decode(&product); err != nil {
		http.Error(w, "Invalid JSON format: "+err.Error(), http.StatusBadRequest)
		return
	}

	if err := cfgService.UpdateProduct(&product); err != nil {
		// Check if it's a validation error
		if _, ok := err.(validator.ValidationErrors); ok {
			http.Error(w, "Validation failed: "+err.Error(), http.StatusBadRequest)
			return
		}
		if strings.Contains(err.Error(), "validation failed") {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]string{"message": "Product updated successfully"})
}

func handleDeleteProduct(w http.ResponseWriter, r *http.Request, cfgService *service.ConfigService) {
	productName := strings.TrimPrefix(r.URL.Path, "/products/")
	if productName == "" || productName == "products" {
		http.Error(w, "Product name is required for deletion", http.StatusBadRequest)
		return
	}

	if err := cfgService.DeleteProduct(productName); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]string{"message": "Product deleted successfully"})
}
