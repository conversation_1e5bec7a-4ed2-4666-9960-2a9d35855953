package models

// A<PERSON><PERSON><PERSON> represents an API key configuration
type Api<PERSON>ey struct {
	Name               string     `json:"name" validate:"required"`
	Key                string     `json:"key" validate:"required"`
	Secret             string     `json:"secret" validate:"required"`
	Token              string     `json:"token" validate:"required"`
	Capacity           int        `json:"capacity"`
	Capbility          []CodeType `json:"capbility"` // 怎样配置jaon枚举类型
	AssignedReceivers  []string   `json:"associated_receivers"`
	AssociatedProducts []string   `json:"associated_products"`
	// 其他相关字段
}

// Product represents a product configuration
type Product struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"description"`
	// 其他相关字段
}

// Receiver represents a receiver instance configuration
type Receiver struct {
	ID               string   `json:"id"`
	IP               string   `json:"ip"`
	AssignedProducts []string `json:"assigned_products"`
	Capacity         int      `json:"capacity"` // Receiver 的容量，可以处理的产品数量
	// 其他相关字段
}

// HttpApi represents an http-api instance configuration
type HttpApi struct {
	IP string `json:"ip"`
	// 其他相关字段
}

// WebsocketApi represents a websocket-api instance configuration
type WebsocketApi struct {
	IP string `json:"ip"`
	// 其他相关字段
}
